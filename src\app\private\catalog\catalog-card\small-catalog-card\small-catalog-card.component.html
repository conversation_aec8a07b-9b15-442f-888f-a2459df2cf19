<ng-container *ngTemplateOutlet="card.type == 'CATEGORY' && !catalogService.intoSubcategory ? category : null; context: { $implicit: card }"></ng-container>
<ng-container *ngTemplateOutlet="card.type == 'CATEGORY' && catalogService.intoSubcategory ? arrow : null; context: { $implicit: card }"></ng-container>
<ng-container *ngTemplateOutlet="card.type == 'PRODUCT' ? product : null; context: { $implicit: card }"></ng-container>
<ng-container *ngTemplateOutlet="card.type == 'FAVORITES' ? favorites : null; context: { $implicit: card }"></ng-container>

<ng-template #category let-card>
  <div class="card category small w-full" longPress (mouseLongPress)="showPreviewSubcategories()" (click)="open(card.idRootCategory, card.idSubCategory, card.isProduct)" [ngClass]="{'white-card': catalogService.intoSubcategory}">
    <div class="w-full">
         <div class="icon">
            <img [src]="card.imageUrl | safe" width="100" height="100" onerror="this.onerror=null;this.src='../../../../assets/icon/placeholder_100x100.png'">
            <!-- Label conteggio sottocategorie per root categories -->
            <div class="subcategories-count" *ngIf="catalogService.isRootView && card.subcategoriesCount !== null && card.subcategoriesCount !== undefined">
              {{card.subcategoriesCount}}
            </div>
         </div>
         <div class="detail">
             <div class="name" [innerHTML]="card.name"></div>
             <div class="open-category"><ion-icon name="caret-down-outline"></ion-icon></div>
         </div>
     </div>
 </div>
</ng-template>

<ng-template #product let-card>
  <div class="card product small" (click)="open(card.idRootCategory,card.idSubCategory, card.isProduct)" [ngClass]="{'white-card': catalogService.intoSubcategory, z3: card.divisionStatusCode === 'Z3'}">
    <div>
         <div class="icon">
             <img [src]="card.imageUrl| safe" width="100" height="100" onerror="this.onerror=null;this.src='../../../../assets/icon/placeholder_100x100.png'">
         </div>
         <div class="detail" [ngClass]="{'focusProd': card.focus === 'S'}">
           <table>
             <tr>
               <td colspan="3">
                <div class="name" [innerHTML]="card.name"></div>
              </td>
             </tr>
             <tr>
               <td><ion-icon name="logo-euro" (click)="cardShowPrices($event)"></ion-icon></td>
               <td><ion-icon name="share-social-outline" (click)="share($event)"></ion-icon></td>
               <td>
                <ion-icon *ngIf="!catalogService.isProspect && card.isFavorite" name="star" (click)="cardRemoveFavorite($event)"></ion-icon>
                <ion-icon *ngIf="!catalogService.isProspect && !card.isFavorite" name="star-outline" (click)="cardAddFavorite($event)"></ion-icon>
               </td>
             </tr>
           </table>
         </div>
     </div>
 </div>
</ng-template>

<ng-template #arrow let-card>
  <div class="card category red-arrow" longPress (mouseLongPress)="showPreviewSubcategories()" (click)="open(card.idRootCategory,card.idSubCategory, card.isProduct)" [ngClass]="{'white-card': catalogService.intoSubcategory}">
    <div class="name" [innerHTML]="card.name"></div>
  </div>
</ng-template>

<ng-template #favorites let-card>
  <div class="card white-card favorites">
    <div>
         <div class="icon">
            <ion-icon name="star"></ion-icon>
         </div>
         <div class="detail">
             <div class="favorites-description">{{'CATALOG.FAVORITES_DESCRIPTION' | translate}}</div>
             <div class="name" [innerHTML]="card.name"></div>
         </div>
     </div>
 </div>
</ng-template>
